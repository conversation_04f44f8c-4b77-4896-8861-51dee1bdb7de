.upload-dataset-modal {
  display: flex;
  height: 100vh;
  width: 100vw;
  justify-content: center;
  align-items: center;

  position: fixed;
  z-index: 10;
}

.upload-dataset-modal .overlay {
  display: flex;
  height: 100%;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.5);

  position: fixed;
  top: 0;
}

.upload-dataset-modal .content {
  display: flex;
  flex-direction: column;
  height: fit-content;
  width: 40%;
  padding: 20px;
  border-radius: 40px;
  background-color: var(--primary-color);
  border: 1px solid #d3d3d3;
  box-shadow: 0 0 0 5px rgb(245, 245, 249, 0.7);
  position: absolute;
  gap: 20px;

  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.upload-dataset-modal .content button:first-child {
  display: flex;
  height: 30px;
  width: 30px;
  padding: 5px;
  border-radius: 50%;
  background-color: var(--primary-color);
  border: 1px solid #d3d3d3;
  transition: 0.5s ease;
  position: absolute;
  right: 20px;
  cursor: pointer;
}

.upload-dataset-modal .content button:first-child:hover {
  background-color: #808080;
}

.upload-dataset-modal .content h2 {
  color: var(--secondary-dark-color);
}

.upload-dataset-modal .content .reminder {
  font-weight: 500;
}

.upload-dataset-modal .content section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: auto;
  width: 100%;
  border: 2px dashed #d3d3d3;
  border-radius: 40px;
  background-color: var(--primary-color);
  transition: 0.5s ease;
}

.upload-dataset-modal .content section:hover {
  background-color: white;
  border: 2px dashed var(--btn-color);
}

.upload-dataset-modal .upload-icon {
  height: 40px;
  background-color: rgba(79, 70, 229, 0.1);
  padding: 10px;
  border-radius: 10px;
}

.upload-dataset-modal .make-prediction-btn {
  padding: 12px 16px;
  border-radius: 40px;
  background-color: var(--forecast-active-color);
  font-weight: 600;
  cursor: pointer;
  transition: 0.5s ease;
}

.upload-dataset-modal .make-prediction-btn:disabled {
  cursor: not-allowed;
  background-color: #d3d3d3;
  color: #808080;
}

.upload-dataset-modal .make-prediction-btn:hover:not(:disabled) {
  background-color: #eecd10;
}

.upload-dataset-modal label {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: 100%;
  align-items: center;
  cursor: pointer;
  border-radius: 40px;
  padding: 40px 50px;
  gap: 10px;
}

.upload-dataset-modal label .selected-file {
  display: flex;
  align-items: center;
  max-width: 100%;
  height: 2.5rem;
  padding-left: 10px;
  border-radius: 40px;
  background-color: white;
  border: 1px solid #d3d3d3;
  margin-top: 5px;
  gap: 5px;
}

.upload-dataset-modal label .selected-file p {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.875rem;
}

.upload-dataset-modal .remove-icon {
  height: 100%;
  padding: 2px;
  padding: 12px;
  border-radius: 0px 40px 40px 0px;
  transition: 0.5s ease;
}

.upload-dataset-modal .remove-icon:hover {
  background-color: rgba(255, 0, 0, 0.2);
}

.upload-dataset-modal label span {
  color: var(--btn-color);
  font-weight: 500;
}

.upload-dataset-modal label .supported-format {
  color: #808080;
  font-size: 0.875rem;
}

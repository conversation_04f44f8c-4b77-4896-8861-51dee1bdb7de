# Generated by Django 5.2 on 2025-06-12 16:06

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0003_alter_customuser_managers_alter_customuser_username'),
    ]

    operations = [
        migrations.CreateModel(
            name='PredictionResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField()),
                ('industry_sector', models.CharField(max_length=200)),
                ('predicted_revenue', models.DecimalField(decimal_places=2, max_digits=20)),
                ('predicted_growth_rate', models.DecimalField(decimal_places=2, max_digits=7)),
                ('predicted_least_crowded', models.IntegerField()),
                ('perform_by_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Trend',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rank', models.IntegerField()),
                ('type', models.CharField(choices=[('short-term', 'Short-Term'), ('mid-term', 'Mid-Term'), ('long-term', 'Long-Term')], max_length=10)),
                ('prediction_result_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='users.predictionresult')),
            ],
        ),
    ]

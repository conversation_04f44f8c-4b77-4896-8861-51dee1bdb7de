* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: none;
  color: var(--primary-dark-color);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
}

.landing-page {
  display: flex;
  flex-direction: row;
  justify-content: center;
  height: 100vh;
  background-color: var(--primary-color);
}

.landing-page section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: start;
}

.landing-page .left-panel {
  display: flex;
  width: 50vw;
  padding: 105px;
}

.landing-page .left-panel h1 {
  letter-spacing: 25px;
  font-size: 2.5rem;
  font-weight: bold;
}

.landing-page .left-panel span {
  margin-left: 20px;
}

.landing-page .left-panel img {
  height: 30px;
}

.landing-page .left-panel h4 {
  font-weight: 600;
  letter-spacing: 3px;
}

.buttons-group button {
  font-size: 20px;
  padding: 16px 24px;
  border-radius: 40px;
  background-color: white;
  color: var(--primary-dark-color);
  transition: ease 0.5s;
  cursor: pointer;
}

.admin-btn:hover {
  background-color: var(--primary-dark-color);
  color: var(--primary-color);
}

.guest-btn:hover {
  background-color: #004718;
  color: var(--primary-color);
}

.buttons-group {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

.extra-style {
  justify-content: center;
  align-items: center;
  height: 20px;
  width: auto;
  background-color: white;
  border-radius: 40px;
  margin-top: 20px;
}

.landing-page .right-panel {
  position: relative;
  display: flex;
  width: 50vw;
  height: 100vh;
}

.big-circle {
  position: absolute;
  height: 300px;
  width: 150px;
  border-radius: 150px 0 0 150px;
  background-color: var(--btn-color);
  box-shadow: 0 0 0 50px rgba(79, 70, 229, 0.25),
    0 0 0 100px rgba(79, 70, 229, 0.25), 0 0 0 150px rgba(79, 70, 229, 0.25);
  align-self: end;
  animation: bigCircleShadow 2s ease;
  transition: ease 0.3s, box-shadow ease 0.6s;
}

.big-circle:hover {
  background-color: var(--forecast-active-color);
  box-shadow: 0 0 0 50px rgba(255, 215, 0, 0.25),
    0 0 0 100px rgba(255, 215, 0, 0.25), 0 0 0 150px rgba(255, 215, 0, 0.25);
}

@keyframes bigCircleShadow {
  0% {
    opacity: 0;
  }
  25% {
    box-shadow: 0 0 0 50px rgba(79, 70, 229, 0.25);
  }
  50% {
    box-shadow: 0 0 0 50px rgba(79, 70, 229, 0.25),
      0 0 0 100px rgba(79, 70, 229, 0.25);
  }
  100% {
    box-shadow: 0 0 0 50px rgba(79, 70, 229, 0.25),
      0 0 0 100px rgba(79, 70, 229, 0.1), 0 0 0 150px rgba(79, 70, 229, 0.25);
  }
}

.small-circle-top {
  position: absolute;
  height: 25px;
  width: 50px;
  border-radius: 0 0 50px 50px;
  background-color: var(--btn-color);
  box-shadow: 0 0 0 10px rgba(79, 70, 229, 0.25),
    0 0 0 20px rgba(79, 70, 229, 0.25), 0 0 0 30px rgba(79, 70, 229, 0.25);
  align-self: end;
  top: 0;
  margin-right: 200px;
  animation: smallCircleShadow 2s ease;
  transition: ease 0.3s, box-shadow ease 0.6s;
}

.small-circle-bottom {
  position: absolute;
  height: 25px;
  width: 50px;
  border-radius: 50px 50px 0 0;
  background-color: var(--btn-color);
  box-shadow: 0 0 0 10px rgba(79, 70, 229, 0.25),
    0 0 0 20px rgba(79, 70, 229, 0.25), 0 0 0 30px rgba(79, 70, 229, 0.25);
  align-self: end;
  bottom: 0;
  margin-right: 200px;
  animation: smallCircleShadow 2s ease;
  transition: ease 0.3s, box-shadow ease 0.6s;
}

.small-circle-top:hover,
.small-circle-bottom:hover {
  background-color: var(--forecast-active-color);
  box-shadow: 0 0 0 10px rgba(255, 215, 0, 0.25),
    0 0 0 20px rgba(255, 215, 0, 0.25), 0 0 0 30px rgba(255, 215, 0, 0.25);
}

@keyframes smallCircleShadow {
  0% {
    opacity: 0;
  }
  25% {
    box-shadow: 0 0 0 10px rgba(79, 70, 229, 0.25);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(79, 70, 229, 0.25),
      0 0 0 20px rgba(79, 70, 229, 0.25);
  }
  100% {
    box-shadow: 0 0 0 10px rgba(79, 70, 229, 0.25),
      0 0 0 20px rgba(79, 70, 229, 0.1), 0 0 0 30px rgba(79, 70, 229, 0.25);
  }
}

.illustration-bottom {
  position: absolute;
  height: 125px;
  width: 125px;
  border-radius: 50%;
  background-color: var(--primary-color);
  border: 2px solid var(--primary-dark-color);
  bottom: 0;
  align-self: end;
  margin-right: 315px;
  margin-bottom: 40px;
  opacity: 0;
  animation: fadeIn 1s ease forwards;
  animation-delay: 0.3s;
  transition: ease 0.3s, box-shadow ease 0.6s;
}

.illustration-middle {
  position: absolute;
  height: 300px;
  width: 300px;
  border-radius: 50%;
  background-color: var(--primary-color);
  border: 2px solid var(--primary-dark-color);
  opacity: 0;
  animation: fadeIn 1s ease forwards;
  animation-delay: 0.5s;
  transition: ease 0.3s, box-shadow ease 0.6s;
}

.illustration-top {
  position: absolute;
  height: 125px;
  width: 125px;
  border-radius: 50%;
  background-color: var(--primary-color);
  border: 2px solid var(--primary-dark-color);
  top: 0;
  align-self: end;
  margin-right: 315px;
  margin-top: 40px;
  opacity: 0;
  animation: fadeIn 1s ease forwards;
  animation-delay: 0.7s;
  transition: ease 0.3s, box-shadow ease 0.6s;
}

.illustration-top:hover,
.illustration-middle:hover,
.illustration-bottom:hover {
  background-color: rgba(79, 70, 229, 0.3);
  border: 2px solid transparent;
  box-shadow: 0 0 0 10px rgba(79, 70, 229, 0.1),
    0 0 0 20px rgba(79, 70, 229, 0.1), 0 0 0 30px rgba(79, 70, 229, 0.1);
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

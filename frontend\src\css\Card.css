.card-top-1,
.card-top-2,
.card-top-3,
.card-top-4,
.card-top-5 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  height: 500px;
  width: 100px;
  border-radius: 50px;
  transition: height 0.2s ease, width 0.6s ease;
}

.card-top-1:hover,
.card-top-2:hover,
.card-top-3:hover,
.card-top-4:hover,
.card-top-5:hover,
.card-top-1.active {
  width: 350px;
}

/* Style the h1 element when the info-container is hovered over. */
.card-top-1 .info-container:hover h1:not(.industry),
.card-top-2 .info-container:hover h1:not(.industry),
.card-top-3 .info-container:hover h1:not(.industry),
.card-top-4 .info-container:hover h1:not(.industry),
.card-top-5 .info-container:hover h1:not(.industry),
.card-top-1.active .info-container.active h1:not(.industry.active) {
  display: flex;
  justify-content: center;
  align-self: flex-start;
  height: 70px;
  width: 70px;
  font-size: 38px;
  background-color: var(--btn-color);
  border-radius: 50px;
  padding: 10px;
  color: var(--primary-color);
  box-shadow: 0px 0px 0px 5px rgba(79, 70, 229, 0.2),
    0px 0px 0px 10px rgba(79, 70, 229, 0.3);
}

.card-top-1 .info-container:hover p,
.card-top-2 .info-container:hover p,
.card-top-3 .info-container:hover p,
.card-top-4 .info-container:hover p,
.card-top-5 .info-container:hover p,
.card-top-1.active .info-container.active p {
  display: block;
  justify-content: center;
  align-items: center;

  color: var(--primary-dark-inactive-color);
  font-size: 1.125rem;
  text-align: justify;
  line-height: 1.8rem;
}

.card-top-1.active.dark .info-container.active.dark p,
.card-top-2.dark .info-container.dark p,
.card-top-3.dark .info-container.dark p,
.card-top-4.dark .info-container.dark p,
.card-top-5.dark .info-container.dark p {
  color: rgba(245, 245, 249, 0.8);
}

.card-top-1 .info-container:hover .positive,
.card-top-1.active .info-container.active .positive {
  color: rgba(79, 70, 229, 0.8);
}

.card-top-1 .info-container:hover .industry,
.card-top-2 .info-container:hover .industry,
.card-top-3 .info-container:hover .industry,
.card-top-4 .info-container:hover .industry,
.card-top-5 .info-container:hover .industry,
.card-top-1.active .info-container.active .industry.active {
  display: block;
  color: var(--primary-dark-color);
  font-size: 32px;
}

.card-top-1.active.dark .info-container.active.dark .industry.active,
.card-top-2.dark .info-container.dark:hover .industry,
.card-top-3.dark .info-container.dark:hover .industry,
.card-top-4.dark .info-container.dark:hover .industry,
.card-top-5.dark .info-container.dark:hover .industry {
  color: var(--primary-color);
}

.card-top-2,
.card-top-2 .info-container,
.card-top-3,
.card-top-3 .info-container {
  height: 443px;
}

.card-top-1 .info-container:hover,
.card-top-2 .info-container:hover,
.card-top-3 .info-container:hover,
.card-top-4 .info-container:hover,
.card-top-5 .info-container:hover,
.card-top-1.active .info-container.active {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 500px;
  width: 350px;
  background-color: var(--forecast-active-color);
  border-radius: 40px;
  box-shadow: 0px 0px 80px 20px rgba(255, 215, 0, 0.2);
  padding: 30px 34px;
}

.card-top-1 .info-container.dark:hover,
.card-top-2 .info-container.dark:hover,
.card-top-3 .info-container.dark:hover,
.card-top-4 .info-container.dark:hover,
.card-top-5 .info-container.dark:hover,
.card-top-1.active.dark .info-container.active.dark {
  background-color: var(--primary-dark-color);
  box-shadow: 0px 0px 80px 20px rgba(35, 35, 51, 0.2);
}

.card-top-4,
.card-top-4 .info-container,
.card-top-5,
.card-top-5 .info-container {
  height: 349px;
}

.circle {
  height: 100px;
  width: 100px;
  background-color: var(--forecast-inactive-color);
  border-radius: 50px;
  box-shadow: 0px 0px 0px 10px rgba(255, 227, 76, 0.2),
    0px 0px 0px 20px rgba(255, 227, 76, 0.3);
}

.circle.dark {
  background-color: var(--primary-dark-inactive-color);
  box-shadow: 0px 0px 0px 10px rgba(79, 79, 92, 0.2),
    0px 0px 0px 20px rgba(79, 79, 92, 0.3);
}

/* Style all the info classname */
.info-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  height: 500px;
  width: 100px;
  background-color: var(--forecast-inactive-color);
  border-radius: 50px 50px 0px 0px;
  position: absolute;
  overflow: hidden;
  transition: height 0.2s ease, width 0.6s ease;
}

.info-container.dark {
  background-color: var(--primary-dark-inactive-color);
}

/* Undisplay the circle when the card-top is hovered over */
.card-top-1:hover .circle,
.card-top-2:hover .circle,
.card-top-3:hover .circle,
.card-top-4:hover .circle,
.card-top-5:hover .circle,
.card-top-1.active .circle.active {
  display: none;
}

.info-container h1:not(.industry) {
  font-size: 290px;
  color: var(--primary-dark-color);
  padding: 0px;
  transition: font-size 0.5s ease, color 0.8s ease, background-color 1s ease,
    box-shadow 1.8s ease;
}

.info-container.dark h1:not(.industry) {
  color: var(--primary-color);
}

.info-container p,
.info-container .industry {
  display: none;
}

.info-container .paragraph-container {
  max-height: 60%;
  overflow: auto;
  margin: 10px 0px 0px 0px;
}

/* Cuztomize the scrollbar */
/* Target the scrollbar */
::-webkit-scrollbar {
  width: 10px; /* Width of the vertical scrollbar */
  height: 10px; /* Height of the horizontal scrollbar */
}

/* Scrollbar track */
::-webkit-scrollbar-track {
  background: rgba(240, 240, 240, 0.3); /* Color of the scrollbar track */
  border-radius: 10px; /* Rounded corners */
}

/* Scrollbar thumb */
::-webkit-scrollbar-thumb {
  background-color: var(--btn-color); /* Color of the scrollbar thumb */
  border-radius: 10px; /* Rounded corners */
  border: 2px solid #f0f0f0; /* Padding around the thumb */
}

/* Scrollbar thumb on hover */
::-webkit-scrollbar-thumb:hover {
  background-color: #0056b3; /* Darker color when hovering */
}

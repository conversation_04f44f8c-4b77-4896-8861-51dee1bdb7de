# Generated by Django 5.2 on 2025-06-12 16:24

import datetime
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0004_predictionresult_trend'),
    ]

    operations = [
        migrations.RenameField(
            model_name='trend',
            old_name='prediction_result_id',
            new_name='prediction_result',
        ),
        migrations.AddField(
            model_name='predictionresult',
            name='created_at',
            field=models.DateTimeField(default=datetime.datetime(2025, 6, 12, 16, 24, 21, 621198, tzinfo=datetime.timezone.utc), editable=False),
        ),
    ]
